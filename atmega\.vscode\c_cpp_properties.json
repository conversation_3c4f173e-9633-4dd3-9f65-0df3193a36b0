//
// !!! WARNING !!! AUTO-GENERATED FILE!
// PLEASE DO NOT MODIFY IT AND USE "platformio.ini":
// https://docs.platformio.org/page/projectconf/section_env_build.html#build-flags
//
{
    "configurations": [
        {
            "name": "PlatformIO",
            "includePath": [
                "E:/OA22/PamAir1/atmega/include",
                "E:/OA22/PamAir1/atmega/src",
                "C:/Users/<USER>/.platformio/packages/framework-arduino-avr/libraries/Wire/src",
                "C:/Users/<USER>/.platformio/packages/framework-arduino-avr/libraries/SoftwareSerial/src",
                "C:/Users/<USER>/.platformio/packages/framework-arduino-avr/cores/arduino",
                "C:/Users/<USER>/.platformio/packages/framework-arduino-avr/variants/standard",
                "E:/OA22/PamAir1/atmega/.pio/libdeps/atmega328p/Unity/src",
                "C:/Users/<USER>/.platformio/packages/framework-arduino-avr/libraries/EEPROM/src",
                "C:/Users/<USER>/.platformio/packages/framework-arduino-avr/libraries/HID/src",
                "C:/Users/<USER>/.platformio/packages/framework-arduino-avr/libraries/SPI/src",
                ""
            ],
            "browse": {
                "limitSymbolsToIncludedHeaders": true,
                "path": [
                    "E:/OA22/PamAir1/atmega/include",
                    "E:/OA22/PamAir1/atmega/src",
                    "C:/Users/<USER>/.platformio/packages/framework-arduino-avr/libraries/Wire/src",
                    "C:/Users/<USER>/.platformio/packages/framework-arduino-avr/libraries/SoftwareSerial/src",
                    "C:/Users/<USER>/.platformio/packages/framework-arduino-avr/cores/arduino",
                    "C:/Users/<USER>/.platformio/packages/framework-arduino-avr/variants/standard",
                    "E:/OA22/PamAir1/atmega/.pio/libdeps/atmega328p/Unity/src",
                    "C:/Users/<USER>/.platformio/packages/framework-arduino-avr/libraries/EEPROM/src",
                    "C:/Users/<USER>/.platformio/packages/framework-arduino-avr/libraries/HID/src",
                    "C:/Users/<USER>/.platformio/packages/framework-arduino-avr/libraries/SPI/src",
                    ""
                ]
            },
            "defines": [
                "PLATFORMIO=60118",
                "ARDUINO_AVR_UNO",
                "F_CPU=16000000L",
                "ARDUINO_ARCH_AVR",
                "ARDUINO=10808",
                "__AVR_ATmega328P__",
                ""
            ],
            "cStandard": "gnu11",
            "cppStandard": "gnu++11",
            "compilerPath": "C:/Users/<USER>/.platformio/packages/toolchain-atmelavr/bin/avr-gcc.exe",
            "compilerArgs": [
                "-mmcu=atmega328p",
                ""
            ]
        }
    ],
    "version": 4
}
